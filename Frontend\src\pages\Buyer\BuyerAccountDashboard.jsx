import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import {
  selectMyDownloads,
  selectMyRequests,
  selectMyBids,
  selectMyOffers,
  selectDashboardStats,
  selectLoading,
  selectErrors,
  fetchBuyerDashboardStats,
  fetchBuyerDownloads,
  fetchBuyerRequests,
  fetchBuyerBids,
  fetchBuyerOffers,
  clearError,
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import Table from "../../components/common/Table";
import LoadingSkeleton, {
  DashboardSkeleton,
  StatCardSkeleton,
} from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaSync } from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import "../../styles/BuyerAccountDashboard.css";
import { MdDashboard } from "react-icons/md";
import { FaDownload } from "react-icons/fa";
import { FaGavel } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaHandshake } from "react-icons/fa";
import ThumbnailImage from "../../components/common/ThumbnailImage";
import { formatStandardDate } from "../../utils/dateValidation";

const BuyerAccountDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const downloads = useSelector(selectMyDownloads);
  const requests = useSelector(selectMyRequests);
  const bids = useSelector(selectMyBids);
  const offers = useSelector(selectMyOffers);
  const stats = useSelector(selectDashboardStats);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchBuyerDashboardStats());
    dispatch(fetchBuyerDownloads({ limit: 2 }));
    dispatch(fetchBuyerRequests({ limit: 2 }));
    dispatch(fetchBuyerBids({ limit: 2 }));
    dispatch(fetchBuyerOffers({ limit: 2 }));
  }, [dispatch]);

  // Handle navigation for stat cards
  const handleStatCardClick = (path) => {
    navigate(path);
  };

  // Handle retry for specific sections
  const handleRetry = (section) => {
    dispatch(clearError(section));
    switch (section) {
      case "stats":
        dispatch(fetchBuyerDashboardStats());
        break;
      case "downloads":
        dispatch(fetchBuyerDownloads({ limit: 2 }));
        break;
      case "requests":
        dispatch(fetchBuyerRequests({ limit: 2 }));
        break;
      case "bids":
        dispatch(fetchBuyerBids({ limit: 2 }));
        break;
      case "offers":
        dispatch(fetchBuyerOffers({ limit: 2 }));
        break;
      default:
        break;
    }
  };

  // Show loading skeleton for initial load
  if (
    loading.stats &&
    loading.downloads &&
    loading.requests &&
    loading.bids &&
    loading.offers
  ) {
    return (
      <div className="BuyerAccountDashboard">
        <SectionWrapper
          icon={<MdDashboard className="BuyerSidebar__icon" />}
          title="Dashboard"
        >
          <DashboardSkeleton />
        </SectionWrapper>
      </div>
    );
  }

  // Column definitions for Downloads table
  const downloadsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Order Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
  ];

  // Column definitions for Requests table
  const requestsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Order Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Requested Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
  ];

  // Column definitions for Bids table
  const bidsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Bid Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Bid Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
  ];

  // Column definitions for Offers table
  const offersColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Offer Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Offer Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
  ];

  // Render function for Downloads rows
  const renderDownloadsRow = (download, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="order-id" className="order-id">
      #{download.orderId?.slice(-8) || download.id}
    </td>,
    <td key="video" className="video">
      <div className="content-item">
        <div className="content-image">
          <ThumbnailImage
            contentId={download?.content?._id}
            thumbnailUrl={download?.thumbnailUrl}
            alt={download.title}
            placeholderWidth={200}
            placeholderHeight={120}
            placeholderText="No image"
          />
        </div>
        <div className="content-info">
          <div className="content-title">{download.title}</div>
          <div className="content-coach">By {download.coach}</div>
        </div>
      </div>
    </td>,
    <td key="date" className="date">
      {formatStandardDate(download.downloadDate)}
    </td>,
    <td key="amount" className="amount">
      ${download.amount?.toFixed(2) || "0.00"}
    </td>,
    <td key="status" className="status">
      <span className="status-badge downloaded">Downloaded</span>
    </td>,
  ];

  // Render function for Requests rows
  const renderRequestsRow = (request, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="order-id" className="order-id">
      #{request._id?.slice(-6) || "N/A"}
    </td>,
    <td key="video" className="video">
      <div className="content-item">
        <div className="content-info">
          <div className="content-title">{request.title}</div>
          <div className="content-coach">
            By{" "}
            {request.seller
              ? `${request.seller.firstName} ${request.seller.lastName}`
              : "Coach"}
          </div>
        </div>
      </div>
    </td>,
    <td key="date" className="date">
      {formatStandardDate(request.createdAt)}
    </td>,
    <td key="amount" className="amount">
      ${(request.budget || request.sellerResponse?.price || 0).toFixed(2)}
    </td>,
    <td key="status" className="status">
      <span className={`status-badge ${request.status?.toLowerCase()}`}>{request.status}</span>
    </td>,
  ];

  // Render function for Bids rows
  const renderBidsRow = (bid, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="order-id" className="order-id">
      #{bid._id?.slice(-6) || "N/A"}
    </td>,
    <td key="video" className="video">
      <div className="content-item">
        <div className="content-image">
          <ThumbnailImage
            contentId={bid.content?._id}
            thumbnailUrl={bid.content?.thumbnailUrl}
            alt={bid.content?.title}
            placeholderWidth={200}
            placeholderHeight={120}
            placeholderText="No image"
          />
        </div>
        <div className="content-info">
          <div className="content-title">{bid.content?.title}</div>
          <div className="content-coach">
            By{" "}
            {bid.content?.seller
              ? `${bid.content.seller.firstName} ${bid.content.seller.lastName}`
              : "Unknown Coach"}
          </div>
        </div>
      </div>
    </td>,
    <td key="date" className="date">
      {formatStandardDate(bid.createdAt)}
    </td>,
    <td key="amount" className="amount">
      ${(bid.amount || 0).toFixed(2)}
    </td>,
    <td key="status" className="status">
      <span className={`status-badge ${bid.status?.toLowerCase()}`}>
        {bid.status}
      </span>
    </td>,
  ];

  // Render function for Offers rows
  const renderOffersRow = (offer, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="order-id" className="order-id">
      #{offer._id?.slice(-6) || "N/A"}
    </td>,
    <td key="video" className="video">
      <div className="content-item">
        <div className="content-image">
          <ThumbnailImage
            contentId={offer.content?._id}
            thumbnailUrl={offer.content?.thumbnailUrl}
            alt={offer.content?.title || "Content"}
            placeholderWidth={200}
            placeholderHeight={120}
            placeholderText="No image"
          />
        </div>
        <div className="content-info">
          <div className="content-title">{offer.content?.title}</div>
          <div className="content-coach">
            By{" "}
            {offer.content?.seller
              ? `${offer.content.seller.firstName} ${offer.content.seller.lastName}`
              : "Unknown Coach"}
          </div>
        </div>
      </div>
    </td>,
    <td key="date" className="date">
      {formatStandardDate(offer.createdAt)}
    </td>,
    <td key="amount" className="amount">
      ${(offer.amount || 0).toFixed(2)}
    </td>,
    <td key="status" className="status">
      <span className={`status-badge ${offer.status?.toLowerCase()}`}>
        {offer.status}
      </span>
    </td>,
  ];

  return (
    <div className="BuyerAccountDashboard">
      <SectionWrapper
        icon={<MdDashboard className="BuyerSidebar__icon" />}
        title="Dashboard"
      >
        {/* Stats Cards */}
        {errors.stats ? (
          <ErrorDisplay
            error={errors.stats}
            onRetry={() => handleRetry("stats")}
            title="Failed to load dashboard stats"
            className="stats-error"
          />
        ) : (
          <div className="stats">
            {loading.stats ? (
              <>
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
              </>
            ) : (
              <>
                <div
                  className="stat-card downloads"
                  onClick={() =>
                    handleStatCardClick("/buyer/account/downloads")
                  }
                  style={{ cursor: "pointer" }}
                >
                  <div className="stat-number">
                    {(stats?.totalDownloads || downloads?.length || 0)
                      .toString()
                      .padStart(2, "0")}
                    <div className="stat-label">Downloads</div>
                  </div>
                  <div className="icon-round">
                    <FaDownload />
                  </div>
                </div>

                <div
                  className="stat-card requests"
                  onClick={() => handleStatCardClick("/buyer/account/requests")}
                  style={{ cursor: "pointer" }}
                >
                  <div className="stat-number">
                    {(stats?.totalRequests || requests?.length || 0)
                      .toString()
                      .padStart(2, "0")}
                    <div className="stat-label">Requests</div>
                  </div>
                  <div className="icon-roundtwo">
                    <MdRequestPage />
                  </div>
                </div>

                <div
                  className="stat-card bids"
                  onClick={() => handleStatCardClick("/buyer/account/bids")}
                  style={{ cursor: "pointer" }}
                >
                  <div className="stat-number">
                    {(stats?.totalBids || bids?.length || 0)
                      .toString()
                      .padStart(2, "0")}
                    <div className="stat-label">Bids</div>
                  </div>
                  <div className="icon-roundthree">
                    <FaGavel />
                  </div>
                </div>

                <div
                  className="stat-card offers"
                  onClick={() => handleStatCardClick("/buyer/account/offers")}
                  style={{ cursor: "pointer" }}
                >
                  <div className="stat-number">
                    {(stats?.totalOffers || offers?.length || 0)
                      .toString()
                      .padStart(2, "0")}
                    <div className="stat-label">Offers</div>
                  </div>
                  <div className="icon-roundfour">
                    <FaHandshake />
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* My Downloads Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Downloads</h3>
            <div className="section-actions">
              <Link to="/buyer/account/downloads" className="view-all">
                View All Downloads
              </Link>
              {errors.downloads && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry("downloads")}
                  title="Retry loading downloads"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.downloads ? (
            <ErrorDisplay
              error={errors.downloads}
              onRetry={() => handleRetry("downloads")}
              title="Failed to load downloads"
            />
          ) : loading.downloads ? (
            <LoadingSkeleton
              count={2}
              height="60px"
              className="table-row-skeleton"
            />
          ) : (
            <Table
              columns={downloadsColumns}
              data={Array.isArray(downloads) ? downloads.slice(0, 2) : []}
              renderRow={renderDownloadsRow}
              className="BuyerAccountDashboard__downloads-table"
              emptyMessage="No downloads yet."
            />
          )}
        </div>

        {/* My Requests Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Requests</h3>
            <div className="section-actions">
              <Link to="/buyer/account/requests" className="view-all">
                View All Requests
              </Link>
              {errors.requests && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry("requests")}
                  title="Retry loading requests"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.requests ? (
            <ErrorDisplay
              error={errors.requests}
              onRetry={() => handleRetry("requests")}
              title="Failed to load requests"
            />
          ) : loading.requests ? (
            <LoadingSkeleton
              count={2}
              height="60px"
              className="table-row-skeleton"
            />
          ) : (
            <Table
              columns={requestsColumns}
              data={Array.isArray(requests) ? requests.slice(0, 2) : []}
              renderRow={renderRequestsRow}
              className="BuyerAccountDashboard__requests-table"
              emptyMessage="No requests yet."
            />
          )}
        </div>

        {/* My Bids Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Bids</h3>
            <div className="section-actions">
              <Link to="/buyer/account/bids" className="view-all">
                View All Bids
              </Link>
              {errors.bids && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry("bids")}
                  title="Retry loading bids"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.bids ? (
            <ErrorDisplay
              error={errors.bids}
              onRetry={() => handleRetry("bids")}
              title="Failed to load bids"
            />
          ) : loading.bids ? (
            <LoadingSkeleton
              count={2}
              height="60px"
              className="table-row-skeleton"
            />
          ) : (
            <Table
              columns={bidsColumns}
              data={Array.isArray(bids) ? bids.slice(0, 2) : []}
              renderRow={renderBidsRow}
              className="BuyerAccountDashboard__bids-table"
              emptyMessage="No bids yet."
            />
          )}
        </div>

        {/* My Offers Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Offers</h3>
            <div className="section-actions">
              <Link to="/buyer/account/offers" className="view-all">
                View All Offers
              </Link>
              {errors.offers && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry("offers")}
                  title="Retry loading offers"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.offers ? (
            <ErrorDisplay
              error={errors.offers}
              onRetry={() => handleRetry("offers")}
              title="Failed to load offers"
            />
          ) : loading.offers ? (
            <LoadingSkeleton
              count={2}
              height="60px"
              className="table-row-skeleton"
            />
          ) : (
            <Table
              columns={offersColumns}
              data={Array.isArray(offers) ? offers.slice(0, 2) : []}
              renderRow={renderOffersRow}
              className="BuyerAccountDashboard__offers-table"
              emptyMessage="No offers yet."
            />
          )}
        </div>
      </SectionWrapper>
    </div>
  );
};

export default BuyerAccountDashboard;
